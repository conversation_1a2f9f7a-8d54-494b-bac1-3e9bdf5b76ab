import { UpdateUserRequest } from 'types/user';
import { apis } from 'utils/axios';
import { AxiosResponse } from 'axios';

export const sentActivation = async ({ body }: any) => {
  try {
    const response = await apis().post('/auth/send-activation', body, {
      baseURL: 'https://api.hathyo.com/auth/api/v1',
    });
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};

export const activate = async ({ body }: any) => {
  try {
    const response = await apis().post('/auth/activate', body, {
      baseURL: 'https://api.hathyo.com/auth/api/v1',
    });
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};

export const resendActivation = async ({ body }: any) => {
  try {
    const response = await apis().post('/auth/resend-activation', body, {
      baseURL: 'https://api.hathyo.com/auth/api/v1',
    });
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};

export const sendOtp = async ({ phoneNumber }: { phoneNumber: string }) => {
  try {
    const response = await apis().post(
      '/sms/send-otp',
      {},
      {
        baseURL: 'https://api.hathyo.com/auth/api/v1',
        params: { phoneNumber },
      }
    );
    console.log('in ra giá trị: ', phoneNumber, "và ", response.data);
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};

// Hàm đăng ký
export const signUp = async ({body}: any) => {
  try {
    const response = await apis().post('/auth/signup', body, {
      baseURL: 'https://api.hathyo.com/auth/api/v1',
    });
    console.log('in ra giá trị đăng ký: ', body ,"và ", response.data);
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};

export const signIn = async ({ body }: any) => {
  try {
    const headers: { Origin?: string } = {};
    if (body.email !== '<EMAIL>') {
      headers.Origin = 'https://hathyo.com';
    }

    const response = await apis().post('/auth/signin', body, {
      baseURL: 'https://api.hathyo.com/auth/api/v1',
      headers,
    });
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};

export const forgotPassword = async ({ body }: any) => {
  try {
    const response = await apis().post('/auth/send-reset-password', body, {
      baseURL: 'https://api.hathyo.com/auth/api/v1',
    });
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};

export const createPassword = async ({ body }: any) => {
  try {
    const response = await apis().post('/auth/init-password', body, {
      baseURL: 'https://api.hathyo.com/auth/api/v1',
    });
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};

export const resetPassword = async ({ body }: any) => {
  try {
    const response = await apis().post('/auth/reset-password', body, {
      baseURL: 'https://api.hathyo.com/auth/api/v1',
    });
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};

export const changePassword = async ({ body }: { body: { currentPassword: string; newPassword: string; confirmationPassword: string } }) => {
  try {
    const response = await apis().post('/users/change-password', body, {
      baseURL: 'https://api.hathyo.com/auth/api/v1',
      headers: {
        Origin: 'https://hathyo.com',
      },
    });
    console.log('changePassword API Response:', {
      status: response.status,
      data: response.data,
      headers: response.headers,
    }); // Detailed debug log
    if (!response?.data) {
      throw new Error('No data returned from the server.');
    }
    return response?.data;
  } catch (e: any) {
    console.error('changePassword API Error:', {
      status: e?.response?.status,
      data: e?.response?.data,
      headers: e?.response?.headers,
      message: e?.message,
    }); 
    throw e?.response?.data || e;
  }
};

export const getUser = async () => {
  try {
    const response = await apis().get('/users/profile');
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};

export const getKeytoDeleteAccount = async () => {
  try {
    console.log('getKeytoDeleteAccount - Making API call to /users/get-delete-key');
    const response = await apis().post('/users/get-delete-key');
    console.log('getKeytoDeleteAccount - API response:', {
      status: response.status,
      data: response.data,
      headers: response.headers
    });
    return response?.data;
  } catch (e: any) {
    console.error('getKeytoDeleteAccount - API error:', {
      status: e?.response?.status,
      data: e?.response?.data,
      message: e?.message
    });
    throw e?.response?.data || e;
  }
};

export const deleteAccount = async ({ body }: { body: { deletedKey: string } }) => {
  try {
    const response = await apis().patch('/users/delete-user', body);
    console.log("in ra giá trị khi xóa tài khoản là: ", response.data);
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};

export const getUserFitness = async () => {
  try {
    const response = await apis().get('/user-fitness');
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};

export const updateUser = async (body: UpdateUserRequest) => {
  try {
    if (!body) {
      throw new Error('Request body is missing');
    }
    console.log('Sending PATCH request to /users/update-profile with body:', JSON.stringify(body, null, 2));
    const response = await apis().patch('/users/update-profile', body, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response?.data;
  } catch (e: any) {
    console.error('Error in updateUser:', JSON.stringify(e?.response?.data || e, null, 2));
    throw e?.response?.data || e;
  }
};

export const postUserFitness = async ({ body }: any) => {
  try {
    const response = await apis().post('/user-fitness', body);
    return response?.data;
  } catch (e: any) {
    throw e?.response?.data || e;
  }
};


export const getChangeEmailKey = async ({ params }: { params: { newEmail: string } }) => {
  try {
    if (!params?.newEmail) {
      throw new Error('newEmail is missing');
    }
    console.log(
      'Sending Post request to /users/get-changeEmail-key with params:',
      JSON.stringify(params, null, 2),
    );
    const response = await apis().post('/users/get-changeEmail-key', null, {
      params: {
        newEmail: params.newEmail,
      },
    });
    console.log("Response data:", response.data);
    return response?.data;
  } catch (e: any) {
    console.error("API error:", e?.response || e);
    throw e?.response?.data || e;
  }
};


export const updatePhone = async ({ body }: { body: { newPhone: string; otp: string } }) => {
  try {
    if (!body?.newPhone || !body?.otp) {
      throw new Error('Missing required parameters: newPhone and otp');
    }
    console.log(
      'Sending PATCH request to /users/update-phone with body:',
      body,
    );
    const response = await apis().patch('/users/update-phone', body);
    console.log("in ra gia tri reponse", response.data)
    return response?.data;
  } catch (e: any) {
    console.error(
      'Error in updatePhone:',
      JSON.stringify(e?.response?.data || e, null, 2),
    );
    throw e?.response?.data || e;
  }
};

export const updateEmail = async ({ body }: { body: { newEmail: string; key: string } }) => {
  try {
    if (!body?.newEmail || !body?.key) {
      throw new Error('Missing required parameters: newEmail and key');
    }
    console.log(
      'Sending PATCH request to /users/update-email with body:',
      JSON.stringify(body, null, 2),
    );
    const response = await apis().patch('/users/update-email', body);
    return response?.data;
  } catch (e: any) {
    console.error(
      'Error in updateEmail:',
      JSON.stringify(e?.response?.data || e, null, 2),
    );
    throw e?.response?.data || e;
  }
};